package com.cchange.ai.friend.ui.screen.splash

import android.os.Parcelable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.cchange.ai.friend.R
import com.cchange.ai.friend.ScreenNode
import com.cchange.ai.friend.biz.fcm.configureFirebaseMessagingTopicIfNeeded
import com.cchange.ai.friend.biz.noti.NotificationActionNavigator
import com.cchange.ai.friend.core.context.findActivity
import com.cchange.ai.friend.core.koin.koinViewModelWithArgs
import com.cchange.ai.friend.res.strings.LocalStrings
import com.cchange.ai.friend.ui.BlankSpacer
import com.cchange.ai.friend.ui.screen.home.HomeArgs
import com.cchange.ai.friend.ui.screen.home.HomeNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import kotlinx.parcelize.Parcelize
import org.orbitmvi.orbit.compose.collectSideEffect

private var splashDisplayCount = 0

@Parcelize
data class SplashArgs(
  val isColdLaunch: Boolean = true,
  val nextLaunchScreenNode: ScreenNode? = null,
) : Parcelable

@Parcelize
data class SplashNode(val args: SplashArgs = SplashArgs()) : ScreenNode("splash") {
  @Composable
  override fun Content(navigator: Navigator) {
    val context = LocalContext.current
    val viewModel: SplashViewModel = koinViewModelWithArgs(args)

    viewModel.collectSideEffect {
      when (it) {
        is SplashSideEffect.NavToHome -> {
          navigator.replaceLast(
            HomeNode(args = HomeArgs().copy(preloadSuccess = it.preloadSuccessful))
          )
          NotificationActionNavigator.handleIntentAction(
            intent = context.findActivity().intent,
            isActivityInForeground = true
          )
        }
      }
    }

    LaunchedEffect(Unit) {
      viewModel.launchedEffect(this, context.findActivity())
    }

    LaunchedEffect(Unit) {
      if (splashDisplayCount > 0) {
        configureFirebaseMessagingTopicIfNeeded(context)
      }
      splashDisplayCount++
    }

    SplashContent()
  }
}

@Composable
fun SplashContent() {
  Scaffold { paddingValues ->
    Column(
      modifier = Modifier
        .padding(paddingValues)
        .fillMaxSize()
    ) {
      Spacer(modifier = Modifier.weight(5.2f))

      Column(
        modifier = Modifier.padding(horizontal = 32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
      ) {
        AsyncImage(
          model = R.mipmap.ic_launcher,
          contentDescription = null,
          modifier = Modifier
            .size(90.dp)
            .clip(RoundedCornerShape(20.dp))
        )
        BlankSpacer(height = 14.dp)

        val strings = LocalStrings.current

        Text(
          text = strings.appName,
          style = MaterialTheme.typography.headlineSmall.copy(fontWeight = FontWeight.Bold)
        )
        BlankSpacer(height = 4.dp)
        Text(text = strings.textSplashContent, style = MaterialTheme.typography.bodyMedium)

        BlankSpacer(height = 64.dp)

        LinearProgressIndicator(
          modifier = Modifier
            .fillMaxWidth()
            .height(14.dp),
          color = MaterialTheme.colorScheme.primary,
          trackColor = MaterialTheme.colorScheme.inversePrimary.copy(.24f),
          strokeCap = StrokeCap.Round,
        )
      }

      Spacer(modifier = Modifier.weight(1.2f))
    }
  }
}
