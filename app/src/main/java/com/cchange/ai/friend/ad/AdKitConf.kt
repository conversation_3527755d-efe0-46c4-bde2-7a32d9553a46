package com.cchange.ai.friend.ad


object AdKitConf {
  const val APPLOVIN_MAX_SDK_KEY = "" /*"zVOMFMwDv1xQdq5lGO0Iq2ST83G1_ykEJKl2u16MzDNmOQZMxp9PQmzlUt0hQXT0dJTiuteqeZRNGwpAa6bvk8"*/
  const val APPLOVIN_MAX_REWARDED_AD = "" /*"28f8de04bdfa0650"*/
  const val APPLOVIN_MAX_APP_OPEN_AD = "" /*"6021636058369b7b"*/
  const val APPLOVIN_MAX_INTERSTITIAL_AD = "" /*"8430d3972ca398b4"*/
  const val APPLOVIN_MAX_BANNER_AD = "" /*"fc9baef096cc4b4b"*/

  const val ADMOB_AD_UNIT_ID_FOR_APP_OPEN: String = "ca-app-pub-3940256099942544/9257395921"
  const val ADMOB_AD_UNIT_ID_FOR_BANNER: String = "ca-app-pub-3940256099942544/9214589741"
  const val ADMOB_AD_UNIT_ID_FOR_NATIVE: String = "ca-app-pub-3940256099942544/2247696110"
  const val ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL: String = "ca-app-pub-3940256099942544/1033173712"
  const val ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL_2: String = "ca-app-pub-3940256099942544/1033173712"
  const val ADMOB_AD_UNIT_ID_FOR_REWARDED: String = "ca-app-pub-3940256099942544/5224354917"

  const val TRAD_PLUS_APP_ID = "0B0222D515D5AC7C66EDBD91D7B4D86A"
  const val TRAD_PLUS_APP_OPEN_AD_UNIT_ID = "37110988C4449C8AD8AB2A777E75792D"
  const val TRAD_PLUS_BANNER_AD_UNIT_ID = "E586A3159B862D826473DECFB2B13436"
  const val TRAD_PLUS_NATIVE_AD_UNIT_ID = "C7135BC1E9D68C2C98CC8C2E79D9511E"
  const val TRAD_PLUS_REWARD_AD_UNIT_ID = "19224326F00428A9BE7AE49047F0B31B"
  const val TRAD_PLUS_INTERSTITIAL_AD_UNIT_ID = "7CB7C92D80A1ECB66684A0B1EB312A20"
  const val TRAD_PLUS_INTERSTITIAL_AD_UNIT_ID_2 = "562A18C83CEAC4E31B0B75B510D53DC1"
}