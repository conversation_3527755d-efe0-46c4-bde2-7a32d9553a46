package com.cchange.ai.friend.ui.screen.splash

import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cchange.ai.friend.IAIGlobalNavigator
import com.cchange.ai.friend.account.AccountManager
import com.cchange.ai.friend.ad.appopen.AppOpenAdFacade
import com.cchange.ai.friend.ad.appopen.AppOpenAdShowStateEvent
import com.cchange.ai.friend.ad.appopen.closeSplashEventFlow
import com.cchange.ai.friend.ad.rewarded.MaxRewardedAdHelper
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.flow.send
import com.cchange.ai.friend.core.log.debugLog
import com.cchange.ai.friend.data.network.apipojocache.BotPluginsCache
import com.cchange.ai.friend.data.repo.ChatRepo
import com.cchange.ai.friend.prefs.DefaultPrefs
import com.cchange.ai.friend.previousKey
import com.cchange.ai.friend.ui.screen.login.LoginNode
import com.cchange.ai.friend.ui.screen.setupaccount.SetupAccountNode
import com.cchange.ai.friend.ui.screen.welcome.WelcomeNode
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.popToRoot
import com.roudikk.guia.extensions.replaceLast
import com.roudikk.guia.extensions.setRoot
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class SplashViewModel(
  private val args: SplashArgs,
  private val chatRepo: ChatRepo,
  private val maxRewardedAdHelper: MaxRewardedAdHelper,
  private val appOpenAdFacade: AppOpenAdFacade,
  private val accountManager: AccountManager,
  private val defaultPrefs: DefaultPrefs,
  private val botPluginsCache: BotPluginsCache,
  private val appCoroutineScope: AppCoroutineScope
) : ViewModel(), ContainerHost<Unit, SplashSideEffect> {

  override val container: Container<Unit, SplashSideEffect> = container(Unit)

  private var preloadChatsJob: Job? = null
  private var currentActivity: Activity? = null

  @OptIn(kotlinx.coroutines.ExperimentalCoroutinesApi::class)
  private fun configure(coroutineScope: CoroutineScope, activity: Activity) {
    currentActivity = activity
    closeSplashEventFlow.resetReplayCache()

    appCoroutineScope.launch(Dispatchers.Default) {
      appOpenAdFacade.tryToLoadAd(activity)
      maxRewardedAdHelper.tryToLoadAd()
    }

    preloadChatsJob = onLoad(args.isColdLaunch, coroutineScope)
  }

  private val preloadSuccessful = MutableStateFlow(false)
  private fun onLoad(isColdLaunch: Boolean, registerCoroutineScope: CoroutineScope) = intent {
    viewModelScope.launch {
      var timeoutAsync: Deferred<*>? = null

      appOpenAdFacade.adShowStateEventFlow.onEach {
        debugLog(tag = "SplashViewModel") { "adShowStateEventFlow: $it" }

        when (it) {
          AppOpenAdShowStateEvent.SkipToShow,
          AppOpenAdShowStateEvent.FailedToShow -> {
            timeoutAsync?.cancel()
            onNavToNextPage(preloadSuccessful.first(), 1)
          }

          AppOpenAdShowStateEvent.Showing -> {
            timeoutAsync?.cancel()
          }

          AppOpenAdShowStateEvent.Finish -> {
            onNavToNextPage(preloadSuccessful.first(), 2)
          }
        }
      }.launchIn(registerCoroutineScope)

      val preloadAsync = if (isColdLaunch) {
        async(Dispatchers.Default) {
          if (accountManager.isLoggedIn()) {
            chatRepo.loadChats()
            preloadSuccessful.emit(true)
          }
        }
      } else {
        botPluginsCache.fetchPluginsAsync()
        null
      }

      timeoutAsync = async(Dispatchers.Default) {
        runCatching {
          if (isColdLaunch) {
            // Use a default timeout of 8 seconds (similar to MaxAppOpenAdHelper's instantLoadTimeoutDelay)
            delay(8_000)
            preloadAsync?.cancel()
            delay(500)
            onNavToNextPage(preloadSuccessful.first(), 3)
          } else {
            delay(1_000)
            onNavToNextPage(false, 4)
          }
        }.onFailure {
          it.printStackTrace()
        }
      }
    }

  }

  private suspend fun onNavToNextPage(preloadSuccessful: Boolean, s: Int) = subIntent {
    debugLog(tag = "SplashViewModel") { "preloadSuccessful: $preloadSuccessful $s" }

    val sessionTokenNotEmpty = !accountManager.sessionToken().isNullOrEmpty()

    val welcomeDone = defaultPrefs.welcomeFinish()

    debugLog(tag = "SplashViewModel") { "welcomeDone: $welcomeDone sessionTokenNotEmpty: $sessionTokenNotEmpty" }

    if (!welcomeDone) {
      IAIGlobalNavigator.transaction {
        if (previousKey !is WelcomeNode) {
          replaceLast(WelcomeNode())
        } else {
          pop()
        }
      }
    } else if (!sessionTokenNotEmpty) {
      val previousIsLoginNodes =
        IAIGlobalNavigator.navigator?.previousKey?.tag()?.contains("login") == true
          || IAIGlobalNavigator.navigator?.previousKey is SetupAccountNode

      if (previousIsLoginNodes) {
        IAIGlobalNavigator.transaction {
          pop()
        }
      } else {
        IAIGlobalNavigator.transaction {
          setRoot(LoginNode())
          popToRoot()
        }
      }
    } else if (args.nextLaunchScreenNode != null) {
      IAIGlobalNavigator.transaction {
        replaceLast(args.nextLaunchScreenNode)
      }
    } else if (args.isColdLaunch) {
      postSideEffect(SplashSideEffect.NavToHome(preloadSuccessful))
    } else {
      IAIGlobalNavigator.transaction {
        pop()
      }
    }
  }

  override fun onCleared() {
    preloadChatsJob?.cancel()
    preloadChatsJob = null
    currentActivity = null
    super.onCleared()
  }

  fun launchedEffect(coroutineScope: CoroutineScope, activity: Activity) {
    configure(coroutineScope, activity)
    closeSplashEventFlow.onEach {
      if (IAIGlobalNavigator.navigator?.currentKey is SplashNode) {
        currentActivity?.let { act ->
          appCoroutineScope.launch {
            appOpenAdFacade.tryToShowAd(act)
          }
        }
      }
    }.launchIn(coroutineScope)
  }
}